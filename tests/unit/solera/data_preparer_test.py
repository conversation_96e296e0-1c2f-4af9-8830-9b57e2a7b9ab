import uuid
from datetime import datetime
from unittest.mock import MagicMock, patch
from contextlib import asynccontextmanager

import pytest

from ciba_participant.activity.models import SoleraParticipantActivityEnum
from ciba_participant.participant.models import SoleraParticipant
from ciba_participant.solera.data_preparer import (
    DataPreparer,
    SoleraData,
    SoleraActivityPayload,
    SoleraCorrectionPayload,
    SoleraActivitiesList,
)


@asynccontextmanager
async def setup_data_preparer_mocks():
    """Set up all the mocks needed for DataPreparer tests."""
    # Create patches for datetime and uuid
    datetime_patch = patch("ciba_participant.solera.data_preparer.datetime")
    uuid_patch = patch("ciba_participant.solera.data_preparer.uuid")

    # Start all patches
    mock_datetime = datetime_patch.start()
    mock_uuid = uuid_patch.start()

    try:
        # Create results dictionary
        mocks = {
            "datetime": mock_datetime,
            "uuid": mock_uuid,
        }

        yield mocks
    finally:
        # Stop all patches explicitly to ensure cleanup
        datetime_patch.stop()
        uuid_patch.stop()


@pytest.fixture
def mock_solera_participant():
    """Create a mock SoleraParticipant for testing."""
    participant = MagicMock(spec=SoleraParticipant)
    participant.solera_data.return_value = {
        "userId": "test-user-123",
        "enrollmentId": "test-enrollment-456",
        "programId": "test-program-789",
    }
    return participant


@pytest.fixture
def mock_datetime_now():
    """Create a mock datetime for consistent testing."""
    return datetime(2024, 1, 15, 10, 30, 45)


@pytest.fixture
def mock_uuid_value():
    """Create a mock UUID for consistent testing."""
    return "test-uuid-123-456-789"


class TestSoleraData:
    """Test SoleraData model."""

    def test_solera_data_creation_with_all_fields(self):
        """Test creating SoleraData with all fields."""
        data = SoleraData(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            referenceId="ref123",
        )
        
        assert data.userId == "user123"
        assert data.enrollmentId == "enrollment456"
        assert data.programId == "program789"
        assert data.referenceId == "ref123"

    def test_solera_data_creation_without_reference_id(self):
        """Test creating SoleraData without referenceId (optional field)."""
        data = SoleraData(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
        )
        
        assert data.userId == "user123"
        assert data.enrollmentId == "enrollment456"
        assert data.programId == "program789"
        assert data.referenceId is None


class TestSoleraCorrectionPayload:
    """Test SoleraCorrectionPayload model."""

    def test_solera_correction_payload_creation(self):
        """Test creating SoleraCorrectionPayload with all fields."""
        payload = SoleraCorrectionPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            referenceId="ref123",
            timestamp="2024-01-15T10:30:45",
            data={"correction": True},
        )
        
        assert payload.userId == "user123"
        assert payload.enrollmentId == "enrollment456"
        assert payload.programId == "program789"
        assert payload.referenceId == "ref123"
        assert payload.timestamp == "2024-01-15T10:30:45"
        assert payload.data == {"correction": True}
        assert payload.eventType == "Correction"

    def test_solera_correction_payload_default_values(self):
        """Test SoleraCorrectionPayload with default values."""
        payload = SoleraCorrectionPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
        )
        
        assert payload.data == {}
        assert payload.eventType == "Correction"


class TestSoleraActivityPayload:
    """Test SoleraActivityPayload model and its field validator."""

    def test_solera_activity_payload_enroll_type(self):
        """Test SoleraActivityPayload with ENROLL activity type."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.ENROLL,
        )
        
        assert payload.data == {"enrollment": True}

    def test_solera_activity_payload_weight_type(self):
        """Test SoleraActivityPayload with WEIGHT activity type."""
        custom_data = {"weight": 75.5, "unit": "kg"}
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.WEIGHT,
            data=custom_data,
        )
        
        # WEIGHT type should preserve the original data
        assert payload.data == custom_data

    def test_solera_activity_payload_play_type(self):
        """Test SoleraActivityPayload with PLAY activity type."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.PLAY,
        )
        
        assert payload.data == {"videoWatched": 1}

    def test_solera_activity_payload_coach_type(self):
        """Test SoleraActivityPayload with COACH activity type."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.COACH,
        )
        
        assert payload.data == {"coachInteraction": 1}

    def test_solera_activity_payload_meals_type(self):
        """Test SoleraActivityPayload with MEALS activity type."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.MEALS,
        )
        
        assert payload.data == {"mealsLogged": 1}

    def test_solera_activity_payload_recipes_type(self):
        """Test SoleraActivityPayload with RECIPES activity type."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.RECIPES,
        )
        
        assert payload.data == {"recipeEducation": 1}

    def test_solera_activity_payload_quiz_type(self):
        """Test SoleraActivityPayload with QUIZ activity type."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.QUIZ,
        )
        
        assert payload.data == {"quizCompleted": 1}

    def test_solera_activity_payload_activity_type(self):
        """Test SoleraActivityPayload with ACTIVITY activity type."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.ACTIVITY,
        )
        
        assert payload.data == {"physicalActivity": 1}

    def test_solera_activity_payload_article_type(self):
        """Test SoleraActivityPayload with ARTICLE activity type."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.ARTICLE,
        )
        
        assert payload.data == {"articleRead": 1}

    def test_solera_activity_payload_group_type(self):
        """Test SoleraActivityPayload with GROUP activity type."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.GROUP,
        )
        
        assert payload.data == {"groupInteraction": 1}

    def test_solera_activity_payload_unknown_type(self):
        """Test SoleraActivityPayload with unknown activity type."""
        # Create a mock enum value that's not handled
        with patch.object(SoleraParticipantActivityEnum, 'UNKNOWN', create=True):
            payload = SoleraActivityPayload(
                userId="user123",
                enrollmentId="enrollment456",
                programId="program789",
                timestamp="2024-01-15T10:30:45",
                activity_type=None,  # This will trigger the else case
            )
            
            assert payload.data == {}

    def test_solera_activity_payload_str_method(self):
        """Test SoleraActivityPayload __str__ method."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            referenceId="ref123",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.ENROLL,
        )
        
        expected_str = "user123 - enrollment456 - ref123 - program789 - 2024-01-15T10:30:45 - {'enrollment': True}"
        assert str(payload) == expected_str


class TestSoleraActivitiesList:
    """Test SoleraActivitiesList model."""

    def test_solera_activities_list_creation_empty(self):
        """Test creating empty SoleraActivitiesList."""
        activities_list = SoleraActivitiesList()
        
        assert activities_list.activities == []

    def test_solera_activities_list_with_activity_payloads(self):
        """Test SoleraActivitiesList with SoleraActivityPayload items."""
        payload = SoleraActivityPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
            activity_type=SoleraParticipantActivityEnum.ENROLL,
        )
        
        activities_list = SoleraActivitiesList(activities=[payload])
        
        assert len(activities_list.activities) == 1
        assert activities_list.activities[0] == payload

    def test_solera_activities_list_with_correction_payloads(self):
        """Test SoleraActivitiesList with SoleraCorrectionPayload items."""
        payload = SoleraCorrectionPayload(
            userId="user123",
            enrollmentId="enrollment456",
            programId="program789",
            timestamp="2024-01-15T10:30:45",
        )
        
        activities_list = SoleraActivitiesList(activities=[payload])
        
        assert len(activities_list.activities) == 1
        assert activities_list.activities[0] == payload
