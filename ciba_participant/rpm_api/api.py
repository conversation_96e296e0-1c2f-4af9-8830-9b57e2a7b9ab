from typing import Optional
from uuid import UUID

from httpx import Async<PERSON><PERSON>, HTTPError, HTTPStatusError, codes

from ciba_participant.common.aws_handler import (
    send_to_sqs,
    SQSNotification,
    NotificationType,
    EmailNotificationEvent,
)
from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.rpm_api.helpers import get_sync_start_date
from ciba_participant.settings import get_settings
from ciba_participant.rpm_api.models import (
    DeviceStatus,
    DeviceStatusEnum,
    DeviceTypeEnum,
    DetailedConnectionStatus,
    DeviceModelEnum,
    SyncProcessing,
    TranstekTrackingData,
    TranstekDeviceInfo,
)
from loguru import logger


settings = get_settings()

PARTICIPANT_MEMBER_TYPE = "participant"


async def get_single_device_status(
    participant_id: str,
    device_type: DeviceTypeEnum,
    device_model: Optional[DeviceModelEnum] = DeviceModelEnum.SCALE,
) -> DetailedConnectionStatus:
    """Given a participant id, and a device type,
    it queries the RPM service and returns detailed
    device status"""
    NOT_CONNECTED = DetailedConnectionStatus(
        healthy=False,
        device=device_type,
        status=DeviceStatusEnum.NOT_CONNECTED,
    )

    params = {
        "type_device": device_type.value,
        "member_type": PARTICIPANT_MEMBER_TYPE,
        "member_id": participant_id,
    }

    if device_model:
        params["device_model"] = device_model.value

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
        params=params,
    ) as client:
        try:
            response = await client.get(
                "/devices/get_status",
            )
            if response.status_code == codes.OK:
                data: dict = response.json()
                device_metadata = data.get("device_metadata") or {}
                healthy = data.get("healthy")

                device_status = DetailedConnectionStatus(
                    token=data.get("token"),
                    account_id=data.get("account_id"),
                    subscription=data.get("subscription"),
                    healthy=healthy,
                    model=data.get("model", None),
                    device=device_type,
                    status=DeviceStatusEnum.CONNECTED
                    if healthy
                    else DeviceStatusEnum.RECONNECT,
                    battery=device_metadata.get("battery"),
                    signal=device_metadata.get("signal"),
                )

                return device_status
        except Exception as ex:
            logger.error(f"Failed to get rpm data: {ex}")
            return NOT_CONNECTED
    return NOT_CONNECTED


async def get_devices_status(participant_id: str) -> list[DeviceStatus]:
    """Given a participant id, it queries the RPM service and returns
    device statuses for each device, as a list"""

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
        params={
            "member_type": PARTICIPANT_MEMBER_TYPE,
            "member_id": participant_id,
        },
    ) as client:
        try:
            response = await client.get(
                "/devices/get_all_status",
            )
            if response.status_code == codes.OK:
                devices_status = [
                    DeviceStatus(
                        device=info["device"],
                        status=info["status"],
                        battery=(info.get("device_metadata") or {}).get("battery"),
                        signal=(info.get("device_metadata") or {}).get("signal"),
                    )
                    for info in response.json()
                    if (info.get("device_metadata") or {}).get("device_type") != "bpm"
                ]

                return devices_status

        except Exception as ex:
            logger.error(f"Failed to get rpm data: {ex}")
            return []
    return []


async def get_devices_status_by_participant_ids(participants: list[str]) -> dict:
    """Given a list of participants, it queries the RPM service and returns
    device statuses for each one as a dict"""

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
    ) as client:
        try:
            params = {
                "member_type": PARTICIPANT_MEMBER_TYPE,
                "members": participants,
            }
            response = await client.post("devices/get_status_by_members", json=params)

            if response.status_code == codes.OK:
                statuses = {}
                for member in response.json():
                    statuses[member["member_id"]] = [
                        DeviceStatus(
                            device=info["device"],
                            status=info["status"],
                        )
                        for info in member["devices"]
                    ]

                return statuses

        except Exception as ex:
            logger.error(f"Failed to get rpm data: {ex}")
            return {}
    return {}


async def sync_measures(participant_id: UUID) -> SyncProcessing:
    """
    Calls the RPM service to initiate a data synchronization for a given participant.

    Args:
        participant_id: The UUID of the participant for whom to sync data.
    Returns:
        A SyncProcessing object indicating the success of the sync request.
    """
    sync_start_date = await get_sync_start_date(participant_id)

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
    ) as client:
        try:
            body = {
                "type_device": "withings",
                "member_type": PARTICIPANT_MEMBER_TYPE,
                "member_id": str(participant_id),
                "start_date": sync_start_date,
            }
            response = await client.post("/devices/sync", json=body)
            response.raise_for_status()

            return SyncProcessing(success=response.json().get("synced"))
        except HTTPStatusError as error:
            message = error.response.json()
            logger.error(
                f"Error processing sync request for {participant_id}: {error.response.status_code} - {message}"
            )
            raise RPMCallError(message.get("detail"))
        except HTTPError as error:
            logger.exception(
                f"An error occurred requesting sync for participant{participant_id}: {error}"
            )
            raise RPMCallError(
                "An error occurred syncing data through RPM service"
            ) from error


async def pair_transtek_device(
    participant_id: UUID,
    participant_email: str,
    serial_number: Optional[str] = None,
    imei: Optional[str] = None,
) -> dict:
    """
    Calls the RPM service to pair a transtek device for a given participant.

    Args:
        participant_id: The UUID of the participant for whom to pair the device.
        serial_number: The serial number to pair.
        imei: The imei of the device to pair.
    Returns:
        A dict containing the pairing result.
    """
    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
    ) as client:
        try:
            body = {
                "member_type": PARTICIPANT_MEMBER_TYPE,
                "member_id": str(participant_id),
                "serial_number": serial_number,
                "imei": imei,
                "email": participant_email,
            }
            response = await client.post("/transtek/pair_device", json=body)
            response.raise_for_status()

            return response.json()
        except HTTPStatusError as error:
            message = error.response.json()
            logger.error(
                f"Error processing pair request for {participant_id}: {error.response.status_code} - {message}"
            )
            raise RPMCallError(message.get("detail"))
        except HTTPError as error:
            logger.exception(
                f"An error occurred requesting pair for participant{participant_id}: {error}"
            )
            raise RPMCallError(
                "An error occurred pairing device through RPM service"
            ) from error


async def get_carrier_list() -> dict:
    """
    Calls the RPM service to get the list of carriers.

    Returns:
        Dict of carriers and tracking url templates
    """
    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
    ) as client:
        try:
            response = await client.get("/transtek/carriers")
            response.raise_for_status()

            return response.json()["carriers"]
        except HTTPStatusError as error:
            message = error.response.json()
            logger.error(
                f"Error getting carrier list: {error.response.status_code} - {message}"
            )
            raise RPMCallError(message.get("detail"))
        except HTTPError as error:
            logger.exception(f"An error occurred getting carrier list: {error}")
            raise RPMCallError(
                "An error occurred getting carrier list through RPM service"
            ) from error


async def update_transtek_tracking_data(
    imei: Optional[str] = None,
    serial_number: Optional[str] = None,
    tracking_data: Optional[TranstekTrackingData] = None,
) -> dict:
    """
    Calls the RPM service to update the tracking data for a transtek device.

    Args:
        imei: The imei of the device to update.
        serial_number: The device id to update.
        tracking_data: The tracking data to update.
    Returns:
        A dict containing the tracking url.
    """

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
    ) as client:
        try:
            body = {
                "imei": imei,
                "serial_number": serial_number,
                "tracking_number": tracking_data.tracking_number,
                "carrier": tracking_data.carrier,
                "member_type": PARTICIPANT_MEMBER_TYPE,
            }
            response = await client.post("/transtek/tracking-data", json=body)
            response.raise_for_status()

            response_data = response.json()

            if _should_send_notification(response_data):
                logger.info(f"Sending tracking notification for {response_data}")
                _send_tracking_notification(response_data, tracking_data)

            return response_data

        except HTTPStatusError as error:
            message = error.response.json()
            logger.error(
                f"Error updating tracking data for transtek device: {error.response.status_code} - {message}"
            )
            raise RPMCallError(message.get("detail"))
        except HTTPError as error:
            logger.exception(
                f"An error occurred updating tracking data for transtek device: {error}"
            )
            raise RPMCallError(
                "An error occurred updating tracking data through RPM service"
            ) from error


def _should_send_notification(response_data: dict) -> bool:
    """
    Determines if a notification should be sent based on response data.

    Args:
        response_data: The response data from the RPM service

    Returns:
        True if notification should be sent, False otherwise
    """
    tracking_url = response_data.get("tracking_url")
    member_type = response_data.get("member_type")
    participant_id = (
        response_data.get("member_id")
        if member_type == PARTICIPANT_MEMBER_TYPE
        else None
    )

    return bool(tracking_url and participant_id)


def _send_tracking_notification(
    response_data: dict, tracking_data: TranstekTrackingData
) -> None:
    """
    Sends a tracking notification via SQS.

    Args:
        response_data: The response data from the RPM service
        tracking_data: The original tracking data
    """
    participant_id = response_data.get("member_id")

    notification = SQSNotification(
        type=NotificationType.SQS,
        email_event=EmailNotificationEvent.TRANSTEK_TRACKING_INFO,
        data={
            "tracking_url": response_data.get("tracking_url"),
            "tracking_number": tracking_data.tracking_number,
            "carrier": tracking_data.carrier,
            "participant_id": str(participant_id),
        },
    )

    send_to_sqs(
        queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
        message_body=notification.model_dump_json(),
    )


async def get_transtek_device_info(participant_id: UUID) -> TranstekDeviceInfo:
    """
    Calls the RPM service to get the device info for a transtek device.

    Args:
        participant_id: The UUID of the participant for whom to get the device info.
    Returns:
        A dict containing the device info.
    """
    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
    ) as client:
        try:
            response = await client.get(
                f"/transtek/device?member_type={PARTICIPANT_MEMBER_TYPE}&member_id={participant_id}"
            )
            response.raise_for_status()

            return TranstekDeviceInfo.model_validate(response.json())
        except HTTPStatusError as error:
            message = error.response.json()
            logger.error(
                f"Error getting device info for transtek device: {error.response.status_code} - {message}"
            )
            raise RPMCallError(message.get("detail"))
        except HTTPError as error:
            logger.exception(
                f"An error occurred getting device info for transtek device: {error}"
            )
            raise RPMCallError(
                "An error occurred getting device info through RPM service"
            ) from error


async def disconnect_device(
    participant_id: UUID,
    device_type: DeviceTypeEnum,
    device_id: Optional[UUID] = None,
    device_model: Optional[DeviceModelEnum] = None,
) -> dict:
    """
    Calls the RPM service to disconnect a given device for a given participant.

    Args:
        participant_id: The UUID of the participant for whom to disconnect the device.
        device_id: RPM device id to disconnect.
        device_type: Type of device to disconnect.
    Returns:
        A dict containing the disconnect result.
    """
    body = {
        "member_type": PARTICIPANT_MEMBER_TYPE,
        "member_id": str(participant_id),
        "type_device": device_type.value,
    }

    if device_model:
        body["device_model"] = device_model.value
    if device_id:
        body["device_id"] = str(device_id)

    async with AsyncClient(
        base_url=settings.RPM_API_URL,
        headers={"X-Auth-Key": settings.RPM_API_KEY},
        timeout=60,
    ) as client:
        try:
            response = await client.post("/devices/disconnect", json=body)
            response.raise_for_status()

            return response.json()
        except HTTPStatusError as error:
            message = error.response.json()
            logger.error(
                f"Error disconnecting device: {error.response.status_code} - {message}"
            )
            raise RPMCallError(message.get("detail"))
        except HTTPError as error:
            logger.exception(f"An error occurred disconnecting device: {error}")
            raise RPMCallError(
                "An error occurred disconnecting device through RPM service"
            ) from error
