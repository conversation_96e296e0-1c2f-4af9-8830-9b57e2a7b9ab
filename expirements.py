from uuid import UUID

from ciba_participant.common.db import init_db, close_db
from ciba_participant.participant.crud import (
    ParticipantMetaRepository,
)
from ciba_participant.participant.pydantic_models import ParticipantAddress
from ciba_participant.rpm_api.api import get_devices_status
from ciba_participant.settings import get_settings
from ciba_participant.solera.mint_vault.api import delete_solera_activity

settings = get_settings()


async def main():
    await init_db()

    # cohorts = await CohortRepository.get_paginated_cohorts(
    #     page=1,
    #     per_page=10,
    #     include={
    #         Include.program_modules,
    #         Include.participants,
    #         Include.program,
    #         Include.created_by,
    #     },
    #     filters=FilterInput(
    #         cohort_status=CohortStatusFilter.ending,
    #     ),
    # )
    #
    # total_pages = cohorts.total_pages
    #
    # print("=" * 80)
    # print("COHORTS DEBUG OUTPUT")
    # print("=" * 80)
    # print(f"Total Pages: {total_pages}")
    # print()
    #
    # for i, cohort in enumerate(cohorts.cohorts, 1):
    #     print(f"[{i}] COHORT: {cohort.name}")
    #     print("-" * 60)
    #     print(f"  ID: {cohort.id}")
    #     print(f"  Status: {cohort.status.value}")
    #     print(f"  Created: {cohort.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Updated: {cohort.updated_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Start Date: {cohort.started_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  End Date: {cohort.end_date.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Participant Limit: {cohort.limit}")
    #     print(f"  Current Participants: {len(cohort.participants)}")
    #     print()
    #
    #     print("=" * 80)
    #     print()

    # session_zoom_ids = await LiveSession.filter(zoom_id__isnull=False).values_list(
    #     "zoom_id", flat=True
    # )
    # session_zoom_ids = set(session_zoom_ids)
    # api_endpoint = settings.SCHEDULE_MANAGER_API_ENDPOINT
    # email_to_remove = "<EMAIL>"
    #
    # async with get_client(api_endpoint) as client:
    #     for zoom_id in session_zoom_ids:
    #         try:
    #             response = await client.get(f"/meetings/{zoom_id}")
    #             json_data = process_response_data(response)
    #
    #             emails = json_data["settings"].get("alternative_hosts", "")
    #             email_list = [
    #                 email.strip() for email in emails.split(";") if email.strip()
    #             ]
    #
    #             # Skip if email to remove is not in the list
    #             if email_to_remove not in email_list:
    #                 print(f"No update needed for meeting {zoom_id}")
    #                 continue
    #
    #             # Remove the email and rejoin
    #             filtered_emails = ";".join(
    #                 email for email in email_list if email != email_to_remove
    #             )
    #
    #             updates = {"settings": {"alternative_hosts": filtered_emails}}
    #
    #             response = await client.patch(f"/meetings/{zoom_id}", json=updates)
    #             process_response_data(response)
    #             print(f"Updated meeting {zoom_id}: removed {email_to_remove}")
    #
    #         except Exception as e:
    #             print(f"Failed to update meeting {zoom_id}: {e}")

    print(
        await delete_solera_activity(
            participant_id="a13bd580-70e1-70ac-ebdf-b62f0692a34f",
            activity_id="c5ed8bac-7b27-4b68-a884-bca2765d0e3d",
        )
    )

    # print(
    #     await ParticipantMetaRepository.update_participant_address(
    #         UUID("719b9530-80b1-70fa-6883-4070b93814ac"),
    #         ParticipantAddress(
    #             street1="987 Main St",
    #             zipCode="15",
    #         ),
    #     )
    # )

    await close_db()


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
